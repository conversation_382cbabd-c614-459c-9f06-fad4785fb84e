import axios from 'axios'
import { ElMessage } from 'element-plus'
import { get, merge } from 'lodash-es'

/** 创建请求实例 */
function createService() {
  // 创建一个 axios 实例命名为 service
  const service = axios.create()
  // 请求拦截
  service.interceptors.request.use(
    (config) => config,
    // 发送失败
    (error) => Promise.reject(error)
  )
  // 响应拦截（可根据具体业务作出相应的调整）
  service.interceptors.response.use(
    (response) => {
      // apiData 是 api 返回的数据
      const apiData = response.data
      // 判断apiData是对象还是数组
      if(Array.isArray(apiData)){
        return apiData
      }
      if(apiData.code == 0){
        return apiData
      }else {
        ElMessage({
        message: apiData.msg,
        type: 'error', 
       })
      }
    },
    (error) => {
      // status 是 HTTP 状态码
      const status = get(error, 'response.status')
      switch (status) {
        case 400:
          error.message = '请求错误'
          break
        case 401:
          // Token 过期时
          error.message = '需要登录'
          break
        case 403:
          error.message = '拒绝访问'
          break
        case 404:
          error.message = '请求地址出错'
          break
        case 408:
          error.message = '请求超时'
          break
        case 500:
          error.message = '服务器内部错误'
          break
        case 501:
          error.message = '服务未实现'
          break
        case 502:
          error.message = '网关错误'
          break
        case 503:
          error.message = '服务不可用'
          break
        case 504:
          error.message = '网关超时'
          break
        case 505:
          error.message = 'HTTP 版本不受支持'
          break
        default:
          break
      }
      ElMessage({
        message: error.message,
        type: 'error', 
      })
      return Promise.reject(error)
    }
  )
  return service
}

/** 创建请求方法 */
function createRequest(service) {
  return function (config) {
    const token = electronApi.globals.getToken();
    const defaultConfig = {
      headers: {
        'Authorization':`Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 0,
      baseURL: import.meta.env.VITE_BASE_URL,
      data: {}
    }
    // 将默认配置 defaultConfig 和传入的自定义配置 config 进行合并成为 mergeConfig
    const mergeConfig = merge(defaultConfig, config)
    return service(mergeConfig)
  }
}

/** 用于网络请求的实例 */
const service = createService()
/** 用于网络请求的方法 */
export const request = createRequest(service)
