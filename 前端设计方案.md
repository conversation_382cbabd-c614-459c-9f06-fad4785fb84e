# Popofifi 大屏客户端前端设计方案

## 1. 前端设计方案

### 为实现跨平台桌面应用功能，采用Electron + Vue3技术栈。进行了桌面应用架构设计，采用主进程-渲染进程分离方案进行了应用架构设计。实现了跨平台桌面应用功能

**技术选型：**
- **核心框架：** Electron 34.2.0 + Vue 3.5.13 + TypeScript 5.7.3
- **构建工具：** Electron-Vite 3.0.0 + Vite 6.1.0
- **架构模式：** 主进程-渲染进程分离架构，通过IPC通信和HTTP服务中继实现功能调用

**设计实现：**
- 主进程负责系统级操作（文件读写、窗口管理、系统API调用）
- 渲染进程负责UI界面和业务逻辑
- 通过3030端口HTTP服务实现主进程API统一入口管理
- 支持开发时浏览器调试和Electron环境无缝切换

### 为实现响应式UI界面，采用Element Plus + 自适应布局工具。进行了组件化UI设计，采用原子化设计方案进行了界面架构设计。实现了多分辨率适配功能

**技术选型：**
- **UI框架：** Element Plus 2.9.11
- **适配方案：** adapter-draft 2.0.0 屏幕适配库
- **样式预处理：** Less 4.3.0
- **动画库：** Animate.css 4.1.1 + GSAP 3.12.7 + Anime.js 4.0.2

**设计实现：**
- 基于1440px基准分辨率进行rem单位适配
- 构建了BaseVideo、BaseDialog、BaseModal等原子组件库
- 实现了响应式布局和多种动画效果
- 支持主题切换和多套UI风格（popfifi、音乐节、配置版本）

### 为实现人脸识别与检测功能，采用MediaPipe + 自研人脸API。进行了计算机视觉集成设计，采用前后端协同方案进行了人脸识别设计。实现了实时人脸检测能力

**技术选型：**
- **视觉处理：** @mediapipe/tasks-vision 0.10.22
- **图像处理：** Canvas API + 自研人脸检测算法
- **摄像头调用：** WebRTC getUserMedia API

**设计实现：**
- 实时视频流捕获和人脸检测（每秒检测一次）
- 人脸质量评估和性别识别
- 支持多人脸检测和最优人脸选择
- 集成人脸边界框绘制和特征点标注

### 为实现AI数字人生成功能，采用RESTful API + 轮询机制。进行了异步任务处理设计，采用任务队列方案进行了图像生成设计。实现了AI换脸和风格化处理功能

**技术选型：**
- **网络请求：** Axios + 自定义拦截器
- **任务管理：** 基于task_id的异步任务轮询
- **图像上传：** 多部分表单上传 + Base64编码

**设计实现：**
- 图片上传到云端存储获取objectKey和avatarId
- 调用数字人生成API创建异步任务
- 轮询任务状态直到生成完成
- 支持多种风格模板和性别自适应选择

### 为实现状态管理和数据持久化，采用Pinia + LocalStorage。进行了状态管理架构设计，采用模块化Store方案进行了数据流设计。实现了全局状态管理能力

**技术选型：**
- **状态管理：** Pinia 3.0.3
- **数据持久化：** LocalStorage + SessionStorage
- **路由管理：** Vue Router 4.5.0

**设计实现：**
- 模块化Store设计：config、single、cart、camera、family等
- 路由参数传递Store实现页面间数据共享
- 用户数据本地缓存和会话管理
- 配置信息动态加载和热更新

### 为实现多活动页面支持，采用动态路由 + 配置驱动。进行了多租户架构设计，采用配置化页面方案进行了活动页面设计。实现了快速活动部署能力

**技术选型：**
- **路由系统：** 三套独立路由配置（popfifi、音乐节、配置版本）
- **配置管理：** 动态配置加载 + 环境变量控制
- **页面模板：** 可复用的活动页面组件

**设计实现：**
- 根据配置文件动态选择应用入口和路由
- 星云时刻活动页面独立实现人脸识别和图像生成流程
- 支持不同活动的UI风格和交互逻辑
- 配置化的背景、Logo、文案等资源管理

### 为实现实时交互和动画效果，采用Canvas + WebGL + 动画库组合。进行了交互体验设计，采用多层动画方案进行了用户体验设计。实现了丰富的视觉交互效果

**技术选型：**
- **3D渲染：** Three.js 0.174.0
- **2D绘图：** Canvas API + dom-to-image 2.6.0
- **动画引擎：** GSAP + Anime.js + CSS3动画

**设计实现：**
- 轮盘抽奖动画效果（快速到慢速的渐变动画）
- 搜索Logo随机位置动画
- 人脸检测框实时绘制
- 页面切换过渡动画和加载动画

### 为实现网络通信和API集成，采用统一API网关 + 错误处理机制。进行了网络架构设计，采用拦截器模式方案进行了请求处理设计。打通了前端应用和后端服务系统

**技术选型：**
- **HTTP客户端：** Axios + 请求/响应拦截器
- **API网关：** 统一baseURL配置 + Token认证
- **错误处理：** Element Plus消息提示 + 统一错误处理

**设计实现：**
- 统一的API请求封装和错误处理
- Token自动注入和刷新机制
- 请求重试和超时处理
- 开发环境和生产环境API地址自动切换

### 为实现应用打包和分发，采用Electron Builder + 自动化构建。进行了CI/CD流程设计，采用多环境构建方案进行了部署设计。实现了自动化应用分发功能

**技术选型：**
- **打包工具：** Electron Builder 25.1.8
- **版本管理：** 自动版本递增脚本
- **更新机制：** Electron Updater 6.3.9

**设计实现：**
- 支持Windows、macOS、Linux多平台打包
- 自动版本号管理和构建信息注入
- 应用自动更新检测和下载
- 测试版和生产版独立构建配置

### 为实现开发调试和性能优化，采用开发工具集成 + 性能监控。进行了开发体验优化设计，采用热重载方案进行了开发效率设计。解决了开发调试和性能瓶颈问题

**技术选型：**
- **开发工具：** ESLint + Prettier + TypeScript
- **调试工具：** Chrome DevTools + Electron DevTools
- **性能优化：** 图片预加载 + 组件懒加载

**设计实现：**
- 代码规范检查和自动格式化
- 浏览器环境开发调试支持
- 图片资源预加载优化
- 内存泄漏防护和定时器清理机制
