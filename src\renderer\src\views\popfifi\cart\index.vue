<template>
  <div class="cart">
    <div class="discount-flag" v-if="isHasDiscount">
      <img class="discount-flag-bg" :src="discountBgImg" />
      <div class="text-wraper">
        <div class="text first-text">
          任意购买<span class="red">{{ configStore.discountObj?.min_quantity }}</span
          >件
        </div>
        <div class="text second-text">
          享全场<span class="red">{{ configStore.discountObj?.discount }}折</span>
        </div>
      </div>
    </div>
    <div class="card-list-wraper">
      <div class="card-list">
        <div v-for="listItem in cardList" :key="listItem.id" class="card-list-item">
          <img class="card-list-item-bg" src="@/assets/cart/card/card-bg.png" alt="" />
          <div class="inner-content">
            <div class="left">
              <img class="bg-container" :src="cardLeftBg" />
              <div class="people-bg">
                <img class="people-bg-inner-img" :src="cardPeopleBg" alt="" />
                <img class="my-small-img" :src="listItem.src" alt="" />
              </div>
              <div class="duo">
                <img class="duo-img" :src="duoImg" alt="" />
                <div class="duo-text">{{ listItem.name }} 系列</div>
              </div>
            </div>
            <div class="right">

              <div class="type-item zheng">
                <div class="type-item-img-wraper">
                  <img :src="listItem.zhengType.img" alt="" />
                </div>
                <div class="type-item-other">
                  <div class="type-name">{{ listItem.zhengType.name }}</div>
                  <div class="type-bottom">
                    <div class="price-content">
                      <template v-if="selectNum >= configStore.discountObj?.min_quantity">
                        <div class="now-price">
                          <div class="text">{{ listItem.zhengType.price }}元/件</div>
                          <div v-if="listItem.zhengType.isHot" class="hot">热销中</div>
                        </div>
                        <div class="ori-price">{{ listItem.zhengType.oriPrice }}元/件</div>
                      </template>
                      <template v-else>
                        <div class="now-price">
                          <div class="text">{{ listItem.zhengType.oriPrice }}元/件</div>
                          <div v-if="listItem.zhengType.isHot" class="hot">热销中</div>
                        </div>
                      </template>
                    </div>
                    <div class="num-content">
                      <div class="op minus" @click="handleClickMinus(listItem, 'zhengType')">-</div>
                      <div class="num">{{ listItem.zhengType.num }}</div>
                      <div class="op plus" @click="handleClickPlus(listItem, 'zhengType')">+</div>
                    </div>
                  </div>
                </div>
              </div>
                            <div class="type-item fuka">
                <div class="type-item-img-wraper">
                  <img :src="listItem.fuKaType.img" alt="" />
                </div>
                <div class="type-item-other">
                  <div class="type-name">{{ listItem.fuKaType.name }}</div>
                  <div class="type-bottom">
                    <div class="price-content">
                      <template v-if="selectNum >= configStore.discountObj?.min_quantity">
                        <div class="now-price">
                          <div class="text">{{ listItem.fuKaType.price }}元/件</div>
                          <div v-if="listItem.fuKaType.isHot" class="hot">热销中</div>
                        </div>
                        <div class="ori-price">{{ listItem.fuKaType.oriPrice }}元/件</div>
                      </template>
                      <template v-else>
                        <div class="now-price">
                          <div class="text">{{ listItem.fuKaType.oriPrice }}元/件</div>
                          <div v-if="listItem.fuKaType.isHot" class="hot">热销中</div>
                        </div>
                      </template>
                    </div>
                    <div class="num-content">
                      <div class="op minus" @click="handleClickMinus(listItem, 'fuKaType')">-</div>
                      <div class="num">{{ listItem.fuKaType.num }}</div>
                      <div class="op plus" @click="handleClickPlus(listItem, 'fuKaType')">+</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="type-item mingxin">
                <div class="type-item-img-wraper">
                  <img :src="listItem.mingxinType.img" alt="" />
                </div>
                <div class="type-item-other">
                  <div class="type-name">{{ listItem.mingxinType.name }}</div>
                  <div class="type-bottom">
                    <div class="price-content">
                      <template v-if="selectNum >= configStore.discountObj?.min_quantity">
                        <div class="now-price">
                          <div class="text">{{ listItem.mingxinType.price }}元/件</div>
                          <div v-if="listItem.mingxinType.isHot" class="hot">热销中</div>
                        </div>
                        <div class="ori-price">{{ listItem.mingxinType.oriPrice }}元/件</div>
                      </template>
                      <template v-else>
                        <div class="now-price">
                          <div class="text">{{ listItem.mingxinType.oriPrice }}元/件</div>
                          <div v-if="listItem.mingxinType.isHot" class="hot">热销中</div>
                        </div>
                      </template>
                    </div>
                    <div class="num-content">
                      <div class="op minus" @click="handleClickMinus(listItem, 'mingxinType')">
                        -
                      </div>
                      <div class="num">{{ listItem.mingxinType.num }}</div>
                      <div class="op plus" @click="handleClickPlus(listItem, 'mingxinType')">+</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="float-bg"></div>
      </div>
    </div>
    <div class="bottom-wraper">
      <div class="all-sum-text">价格合计：{{ selectPrice }}元</div>
      <div class="discount-sum-text">
        已选择{{ selectNum }}件，<span class="green">共优惠{{ discountMoney }}元</span>
      </div>
      <div
        :class="[
          'pay-button',
          {
            'pay-button-loading': createOrderLoading
          }
        ]"
        @click="handleClickToPay"
      >
        <img v-if="!createOrderLoading" class="pay-btn" :src="payBtnImg" alt="" />
        <div v-else class="create-order">
          <div class="tips">订单创建中...</div>
          <div class="loading">
            <base-loading></base-loading>
          </div>
        </div>
      </div>
      <div v-if="!createOrderLoading" class="back-btn" @click="handleBack">
        <el-icon size="60rem"><Back /></el-icon>返回首页
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import cardLeftBg from '@/assets/cart/card/juxing.png'
import cardPeopleBg from '@/assets/cart/card/people-bg.png'
import duoImg from '@/assets/cart/card/duo.png'
import fuKaImg from '@/assets/cart/card/fuka.png'
import zhengImg from '@/assets/cart/card/zheng.png'
import mingxinImg from '@/assets/cart/card/mingxin.png'
import tuoYuanImg from '@/assets/cart/card/tuoyuan.png'
import discountBgImg from '@/assets/cart/sale-bg.png'
import payBtnImg from '@/assets/cart/pay-btn.png'
import { fenToYuan, formatPrice } from '@/utils/index'
import { ElMessage } from 'element-plus'
import { useCartStore } from '@/store/cart'
import { useConfigStore } from '@/store/config'
import { useRouteParamsStore } from '@/store/routeParams'
import { Back } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { createOrder } from '@/apis/9_9'
import BaseLoading from '@/components/base/Loading/index.vue'
import Big from 'big.js'
const cartStore = useCartStore()
const configStore = useConfigStore()
const routeParamsStore = useRouteParamsStore()
const router = useRouter()
const route = useRoute()
const cardList = ref([])
const discountMoney = ref(0)

const original_amount = ref(0)

const createOrderLoading = ref(false)

const selectNum = computed(() => {
  // 使用 Big 初始化累加器（初始值为0）
  let total = new Big(0)
  cardList.value.forEach((item) => {
    // 将每个类型的数量转换为 Big 实例后累加
    const fuKaNum = new Big(item.fuKaType.num)
    const zhengNum = new Big(item.zhengType.num)
    const mingxinNum = new Big(item.mingxinType.num)
    total = total.plus(fuKaNum).plus(zhengNum).plus(mingxinNum)
  })
  // 转换为普通数值返回（保持与原逻辑一致的返回类型）
  return total.toNumber()
})

const selectPrice = computed(() => {
  // 使用 Big 初始化原价总和和折扣价总和（初始值为0）
  let oriAll = new Big(0)
  let sum = new Big(0)

  // 计算原价总和（oriAll）
  cardList.value.forEach((item) => {
    // 福卡类型原价计算
    const fuKaOri = new Big(item.fuKaType.oriPrice).times(item.fuKaType.num)
    // 通行证类型原价计算
    const zhengOri = new Big(item.zhengType.oriPrice).times(item.zhengType.num)
    // 明信片类型原价计算
    const mingxinOri = new Big(item.mingxinType.oriPrice).times(item.mingxinType.num)
    // 累加到原价总和
    oriAll = oriAll.plus(fuKaOri).plus(zhengOri).plus(mingxinOri)
  })
  original_amount.value = oriAll.toNumber()

  // 根据是否满足折扣条件计算最终价格
  if (isHasDiscount.value && selectNum.value >= configStore.discountObj?.min_quantity) {
    // 计算折扣价总和（sum）
    cardList.value.forEach((item) => {
      // 福卡类型折扣价计算
      const fuKaPrice = new Big(item.fuKaType.price).times(item.fuKaType.num)
      // 通行证类型折扣价计算
      const zhengPrice = new Big(item.zhengType.price).times(item.zhengType.num)
      // 明信片类型折扣价计算
      const mingxinPrice = new Big(item.mingxinType.price).times(item.mingxinType.num)
      // 累加到折扣价总和
      sum = sum.plus(fuKaPrice).plus(zhengPrice).plus(mingxinPrice)
    })
    // 计算优惠金额（原价总和 - 折扣价总和）
    discountMoney.value = oriAll.minus(sum).toNumber()
  } else {
    // 不满足折扣时，最终价格等于原价总和
    sum = oriAll
    discountMoney.value = 0
  }

  // 转换为普通数值返回（保持与模板显示一致）
  return sum.toNumber()
})

// 是否有折扣
const isHasDiscount = computed(() => {
  if (configStore.discountObj?.coupon_id == 0) {
    return false
  }
  return true
})

onMounted(() => {
  if(route.query.isFromCartPay){
   cardList.value =  cartStore.cartList;
    return;
  }
  handleInitCardList()
})


const handleInitCardList = () => {
  let goodList = configStore.goodList
  const fukaGoodItem = goodList.find((item) => item.number == 'V1_NFC_MINI')
  const zhengGoodItem = goodList.find((item) => item.number == 'V1_NFC')
  const mingxinGoodItem = goodList.find((item) => item.number == 'V1_PHOTO')

  console.log(fukaGoodItem, zhengGoodItem, mingxinGoodItem)
  let discountObj = configStore.discountObj
  // 使用 Big 计算折扣率（打几折即除以10）
  const finDiscountBig = new Big(discountObj.discount).div(10) // 例如：8折 → 8/10=0.8（精确计算）
  let favorList = []
  if (cartStore.favorList.length) {
    favorList = cartStore.favorList
  } else {
    favorList = JSON.parse(localStorage.getItem('favorList')) || []
  }
  const selectedArr = favorList.filter((item) => item.favor)
  const unSelectedArr = favorList.filter((item) => !item.favor)
  const newArr = [...selectedArr, ...unSelectedArr]
  cardList.value = newArr.map((item) => {
    return {
      id: item.subtask_id,
      name: item.style_name,
      src: item.result_url,
      style: item.style,
      pic_url: item.result_oss,
      discount_id: discountObj.coupon_id,
      fuKaType: {
        isHot: true,
        name: '通行证/通行福袋',
        num: 0,
        price: fenToYuan(new Big(fukaGoodItem.price).times(finDiscountBig).toNumber()),
        goodId: fukaGoodItem.id,
        goodType: fukaGoodItem.number,
        oriPrice: Number(formatPrice(fukaGoodItem.price)),
        img: fuKaImg
      },
      zhengType: {
        isHot: false,
        name: '通行证典藏款',
        num: 0,
        price: fenToYuan(new Big(zhengGoodItem.price).times(finDiscountBig).toNumber()),
        goodId: zhengGoodItem.id,
        goodType: zhengGoodItem.number,
        oriPrice: Number(formatPrice(zhengGoodItem.price)),
        img: zhengImg
      },
      mingxinType: {
        isHot: false,
        name: '明信片',
        num: 0,
        price: fenToYuan(new Big(mingxinGoodItem.price).times(finDiscountBig).toNumber()),
        goodId: mingxinGoodItem.id,
        goodType: mingxinGoodItem.number,
        oriPrice: Number(formatPrice(mingxinGoodItem.price)),
        img: mingxinImg
      }
    }
  })
}

const handleClickMinus = (item, type) => {
  if (item[type].num > 0) {
    item[type].num--
  } else {
    ElMessage({
      message: '数量不能小于0',
      type: 'warning'
    })
    return
  }
}
const handleClickPlus = (item, type) => {
  item[type].num++
}
const handleClickToPay = async () => {
  if (createOrderLoading.value) {
    return
  }
    if(selectNum.value<=0){
     ElMessage({
      message: '数量不能小于0',
      type: 'warning'
    })
    return
  }
  console.log(routeParamsStore.routeParamsData, '数据')
  //  把cardList 里面 fuKaType zhengType mingxinType
  const discount_id = isHasDiscount.value ? configStore.discountObj.coupon_id : 0
  const business_data = cardList.value.reduce((pre, current) => {
    console.log(current, '这是current')
    console.log(pre, '这是pre')
    if (current.fuKaType.num > 0) {
      const item = current.fuKaType
      pre.push({
        goods_id: item.goodId,
        goods_num: item.num,
        goods_type: item.goodType,
        style: current.style,
        pic_url: current.pic_url,
        discount_id: discount_id
      })
    }
    if (current.zhengType.num > 0) {
      const item = current.zhengType
      pre.push({
        goods_id: item.goodId,
        goods_num: item.num,
        goods_type: item.goodType,
        style: current.style,
        pic_url: current.pic_url,
        discount_id: discount_id
      })
    }
    if (current.mingxinType.num > 0) {
      const item = current.mingxinType
      pre.push({
        goods_id: item.goodId,
        goods_num: item.num,
        goods_type: item.goodType,
        style: current.style,
        pic_url: current.pic_url,
        discount_id: discount_id
      })
    }
    return pre
  }, [])

  const params = {
    business_data,
    payment_amount: new Big(selectPrice.value).times(100).toNumber(),
    original_amount: new Big(original_amount.value).times(100).toNumber(),
    discount_id: discount_id,
    origi_pic_url: routeParamsStore.routeParamsData.objectKey,
    digital_avatar_id: routeParamsStore.routeParamsData.avatarId
  }
  createOrderLoading.value = true
  const res = await createOrder(params)
  createOrderLoading.value = false
  if (res) {
    console.log(cartStore,'hahahh')
     cartStore.setPayCodeUrl(res.data.qrcode_url);
     cartStore.setBusinessOrderId(res.data.business_order_id)
     cartStore.setCartList(cardList.value)
     router.push({ path: '/cartPay' });
  }
}
const handleBack = () => {
  cartStore.setCartList([])
  cartStore.setFavorList([])
  localStorage.removeItem('favorList')
  router.push({ path: '/welcome' })
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'MyCartFont';
  src: url('@/assets/fonts/HaiPaiQiangDiaoGunShiJian-2.otf') format('truetype');
}
@font-face {
  font-family: 'DouyinSansBold';
  src: url('@/assets/fonts/DouyinSansBold.ttf') format('truetype');
  font-style: normal;
}
.cart {
  width: 100%;
  height: 100%;
  background: url('@/assets/cart/bg.png') no-repeat center center; // 新增背景图
  background-size: cover; // 保持背景图覆盖
  font-family: 'DouyinSansBold', sans-serif;
  display: flex;
  flex-direction: column;
  position: relative;
  .card-list-wraper {
    height: 80%;
    width: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    .card-list {
      width: 100%;
      height: 87.3%;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 30rem;
      padding: 0rem 41.33rem 26% 64rem;
      box-sizing: border-box;
      &-item {
        width: 100%;
        position: relative;
        &-bg {
          width: 100%;
          height: auto;
        }
        .inner-content {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          padding: 41.33rem 66.66rem 35.33rem 44rem;
          box-sizing: border-box;
          display: flex;
          gap: 44rem;
          .left {
            height: 100%;
            position: relative;
            .bg-container {
              height: 100%;
              width: auto;
            }
            .people-bg {
              position: absolute;
              top: 30rem;
              right: 27.33rem;
              width: 328rem;
              height: 554rem;
              &-inner-img {
                width: 100%;
                height: 100%;
              }
              .my-small-img {
                position: absolute;
                bottom: 100rem;
                right: 50rem;
                max-width: 278rem;
                max-height: 392rem;
              }
            }
            .duo {
              position: absolute;
              left: 26rem;
              bottom: 24rem;
              &-img {
                width: 344.66rem;
                height: auto;
              }
              &-text {
                position: absolute;
                width: 100%;
                left: 0;
                bottom: 2.66rem;
                font-weight: bold;
                font-size: 41.33rem;
                color: #ffffff;
                text-align: center;
              }
            }
          }
          .right {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            gap: 33.33rem;
            .type-item {
              width: 100%;
              display: flex;
              &-img-wraper {
                width: 28%;
                display: flex;
                justify-content: flex-start;
                img {
                  width: 164.66rem;
                  height: auto;
                }
              }
              &-other {
                height: 100%;
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: 28rem;
                .type-name {
                  font-weight: bold;
                  font-size: 40rem;
                  color: #0842cc;
                  line-height: 44.66rem;
                  text-align: left;
                  text-shadow: 0 0 1.5rem rgba(8, 66, 204, 23.3); // 添加文字阴影增强效果
                }
                .type-bottom {
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  .price-content {
                    display: flex;
                    flex-direction: column;
                    gap: 8.66rem;
                    .now-price {
                      display: flex;
                      align-items: flex-end;
                      gap: 30.66rem;
                      .text {
                        font-weight: bold;
                        font-size: 28.66rem;
                        color: #0d3baa;
                        line-height: 26.66rem;
                      }
                      .hot {
                        background: url('@/assets/cart/card/tuoyuan.png') no-repeat center center; // 新增背景图
                        background-size: cover; // 保持背景图覆盖
                        width: 90.66rem;
                        height: 31.33rem;
                        line-height: 34.33rem;
                        font-weight: bold;
                        font-size: 22.66rem;
                        color: #f91f1b;
                      }
                    }
                    .ori-price {
                      font-weight: bold;
                      font-size: 18.66rem;
                      color: #2a72ca;
                      line-height: 16.66rem;
                      text-align: left;
                      text-decoration: line-through;
                      text-decoration-color: #8948dd;
                      text-decoration-thickness: 2.66rem;
                    }
                  }
                  .num-content {
                    display: flex;
                    justify-content: flex-end;
                    align-items: flex-end;
                    .op {
                      font-family: 'MyCartFont', sans-serif;
                      width: 38.66rem;
                      height: 38.66rem;
                      background: #67c6fd;
                      border-radius: 50%;
                      font-weight: 400;
                      font-size: 28rem;
                      color: #ffffff;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    }
                    .plus {
                      font-size: 20rem;
                    }
                    .num {
                      width: 130rem;
                      font-weight: bold;
                      font-size: 60.66rem;
                      color: #2772c8;
                      line-height: 40rem;
                    }
                  }
                }
              }
            }
            .zheng {
              .type-item-img-wraper {
                img {
                  width: 181.33rem;
                }
              }
            }
          }
        }
      }
      .float-bg {
        position: fixed;
        left: 0;
        bottom: 19%;
        height: 10%;
        width: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 20%, rgba(0, 0, 0, 0) 100%);
        -webkit-mask: linear-gradient(
          to top,
          rgba(0, 0, 0, 1) 0%,
          rgba(0, 0, 0, 0.8) 30%,
          rgba(0, 0, 0, 0.5) 60%,
          rgba(0, 0, 0, 0) 100%
        );
        backdrop-filter: blur(10rem) brightness(1);
        -webkit-backdrop-filter: blur(10rem) brightness(1);
        z-index: 2;
        pointer-events: none;
      }
    }
  }
  .bottom-wraper {
    flex: 1;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-direction: column;
    font-weight: bold;
    font-size: 30.66rem;
    color: #ffffff;
    letter-spacing: 3rem;
    .all-sum-text {
      line-height: 30rem;
      padding-bottom: 10rem;
    }
    .discount-sum-text {
      padding-bottom: 15rem;
      .green {
        font-size: 34.66rem;
        color: #19ed3c;
      }
    }
    .pay-button {
      width: 469.33rem;
      height: 180rem;
      .pay-btn {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .create-order {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20rem;
        .tips {
          font-size: 50rem;
          color: #ffffff;
        }
      }
    }
    .pay-button-loading {
      width: 100%;
    }
    .back-btn {
      position: fixed;
      left: 30rem;
      top: 125rem;
      padding: 20rem 0rem 30rem 0rem;
      font-weight: bold;
      font-size: 50rem;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15rem;
    }
  }
  .discount-flag {
    position: absolute;
    top: 62.66rem;
    right: 22rem;
    z-index: 10;
    &-bg {
      width: 595.33rem;
      height: auto;
    }
    .text-wraper {
      position: absolute;
      top: 58.66rem;
      right: 58.66rem;
      font-family: 'MyCartFont', sans-serif;
      letter-spacing: 5rem;
      .text {
        font-size: 40rem;
        color: #ffffff;
        text-stroke: 2rem #009cff;
        -webkit-text-stroke: 2rem #009cff;
        // 斜体
        transform: skewX(345deg);
        line-height: 55rem;
        .red {
          font-size: 60rem;
          color: #f91f1b;
        }
      }
    }
  }
}
</style>
