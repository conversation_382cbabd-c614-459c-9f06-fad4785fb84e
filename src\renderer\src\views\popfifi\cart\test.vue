<template>
  <div class="test">
    <!-- From Uiverse.io by catraco -->
    <div
      title="Like"
      :class="[
        'heart-container',
        {
          'animate-heart': isHeartAnimate
        }
      ]"
      @click="handleClickFavor"
    >
      <div class="svg-container">
        <svg xmlns="http://www.w3.org/2000/svg" class="svg-outline" viewBox="0 0 24 24">
          <path
            d="M17.5,1.917a6.4,6.4,0,0,0-5.5,3.3,6.4,6.4,0,0,0-5.5-3.3A6.8,6.8,0,0,0,0,8.967c0,4.547,4.786,9.513,8.8,12.88a4.974,4.974,0,0,0,6.4,0C19.214,18.48,24,13.514,24,8.967A6.8,6.8,0,0,0,17.5,1.917Zm-3.585,18.4a2.973,2.973,0,0,1-3.83,0C4.947,16.006,2,11.87,2,8.967a4.8,4.8,0,0,1,4.5-5.05A4.8,4.8,0,0,1,11,8.967a1,1,0,0,0,2,0,4.8,4.8,0,0,1,4.5-5.05A4.8,4.8,0,0,1,22,8.967C22,11.87,19.053,16.006,13.915,20.313Z"
          ></path>
        </svg>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          :class="[
            'svg-filled',
            {
              'to-fill': isFavor
            }
          ]"
          viewBox="0 0 24 24"
        >
          <path
            d="M17.5,1.917a6.4,6.4,0,0,0-5.5,3.3,6.4,6.4,0,0,0-5.5-3.3A6.8,6.8,0,0,0,0,8.967c0,4.547,4.786,9.513,8.8,12.88a4.974,4.974,0,0,0,6.4,0C19.214,18.48,24,13.514,24,8.967A6.8,6.8,0,0,0,17.5,1.917Z"
          ></path>
        </svg>

      </div>
    </div>
    <!-- <button
      :class="[
        'bubbly-button',
        {
          animate: isAnimate
        }
      ]"
      @click="handleClick"
    >
      Like
    </button> -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const isFavor = ref(false)
const isHeartAnimate = ref(false) // 新增爱心动画状态
const isAnimate = ref(false)
const handleClickFavor = () => {
  isFavor.value = !isFavor.value
  if (isFavor.value) {
    isHeartAnimate.value = true
    setTimeout(() => {
      isHeartAnimate.value = false
    }, 1500)
  } else {
    isHeartAnimate.value = false
  }
}

const handleClick = () => {
  isAnimate.value = false
  isAnimate.value = true
  setTimeout(() => {
    isAnimate.value = false
  }, 700)
}
</script>

<style lang="less" scoped>
.test {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
/* From Uiverse.io by catraco */
.heart-container {
  --heart-color: rgb(189, 91, 255);
  position: relative;
  width: 150px; // 修正过大尺寸（原200rem会导致容器不可见）
  height: 150px;
  overflow: visible; // 新增允许内容溢出
  transition: 0.3s;
  .svg-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative; // 确保SVG定位基准
    /* 新增射线动画 */
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1; /* 确保在爱心上方 */
  }

  /* 创建8条不同角度的射线 */
  &::after {
       content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
     background: repeating-conic-gradient(
        transparent 0deg,
        transparent calc(360deg/8 - 1deg),  /* 关键修改：缩小角度差（5°→3°），线条更细 */
        var(--heart-color) calc(360deg/8 - 1deg),
        var(--heart-color) calc(360deg/8),
        transparent calc(360deg/8)
      );
      /* 关键修改：调整mask范围，让射线从爱心外部开始 */
      mask: radial-gradient(
        circle,
        transparent 80%,  /* 中心80%区域隐藏（爱心占据约80%宽度） */
        white 80%  /* 80%到100%区域显示射线 */
      );
      /* 关键修改：初始缩放值调整为0.8（对应mask的80%位置） */
      opacity: 0;
      transform: scale(0.8);  /* 从爱心边缘外开始 */
    }
  }

  .svg-outline {
    fill: var(--heart-color);
    position: absolute;
  }
  .svg-filled {
    fill: var(--heart-color);
    position: absolute;
    animation: keyframes-svg-filled 1s;
    display: none;
  }

  .to-fill {
    display: block;
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    width: 400%; // 增大覆盖范围
    height: 400%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%); // 精确中心定位
    z-index: -1;
    background-repeat: no-repeat;
    display: none;
  }

  &.animate-heart {
    // 顶部气泡组
    &:before {
      display: block;
      animation: explode 0.8s ease-out forwards;
      background-image:
        // 中心区域（实圈+虚圈）
        radial-gradient(circle at 50% 50%, var(--heart-color) 8%, transparent 8%),
        // 中心实圈
        radial-gradient(circle at 50% 50%, transparent 6%, var(--heart-color) 6%, transparent 12%),
        // 中心虚圈
        // 主方向（8个基础方向）
        radial-gradient(circle at 50% 20%, var(--heart-color) 6%, transparent 6%),
        // 上
        radial-gradient(circle at 50% 80%, var(--heart-color) 6%, transparent 6%),
        // 下
        radial-gradient(circle at 20% 50%, var(--heart-color) 6%, transparent 6%),
        // 左
        radial-gradient(circle at 80% 50%, var(--heart-color) 6%, transparent 6%),
        // 右
        radial-gradient(circle at 35% 35%, var(--heart-color) 6%, transparent 6%),
        // 左上
        radial-gradient(circle at 65% 35%, var(--heart-color) 6%, transparent 6%),
        // 右上
        radial-gradient(circle at 35% 65%, var(--heart-color) 6%, transparent 6%),
        // 左下
        radial-gradient(circle at 65% 65%, var(--heart-color) 6%, transparent 6%),
        // 右下
        // 对应虚圈（每个实圈配一个虚圈）
        radial-gradient(circle at 50% 20%, transparent 4%, var(--heart-color) 4%, transparent 10%),
        // 上虚圈
        radial-gradient(circle at 50% 80%, transparent 4%, var(--heart-color) 4%, transparent 10%),
        // 下虚圈
        radial-gradient(circle at 20% 50%, transparent 4%, var(--heart-color) 4%, transparent 10%),
        // 左虚圈
        radial-gradient(circle at 80% 50%, transparent 4%, var(--heart-color) 4%, transparent 10%),
        // 右虚圈
        radial-gradient(circle at 35% 35%, transparent 4%, var(--heart-color) 4%, transparent 10%),
        // 左上虚圈
        radial-gradient(circle at 65% 35%, transparent 4%, var(--heart-color) 4%, transparent 10%),
        // 右上虚圈
        radial-gradient(circle at 35% 65%, transparent 4%, var(--heart-color) 4%, transparent 10%),
        // 左下虚圈
        radial-gradient(
            circle at 65% 65%,
            transparent 4%,
            var(--heart-color) 4%,
            transparent 10%
          ); // 右下虚圈

      background-size: 18% 18%; // 调整尺寸平衡数量与大小
    }

    // 底部气泡组（可根据需要添加）
    &:after {
      display: none; // 暂时合并为单组动画更易调试
    }
    .svg-container::after {
     animation: ray-spread 0.6s ease-out forwards;
 }
  }
}

@keyframes keyframes-svg-filled {
  0% {
    transform: scale(0);
  }

  25% {
    transform: scale(1.2);
  }

  50% {
    transform: scale(1);
    filter: brightness(1.5);
  }
}


@keyframes ray-spread {
  0% {
    opacity: 0;
    transform: scale(0.5);  /* 保持从爱心外开始 */
  }
  50% {
    opacity: 0.8;
    transform: scale(1.5);  /* 关键修改：增大中间缩放（2.0→2.5） */
  }
  100% {
    opacity: 0;
    transform: scale(2.5);  /* 关键修改：增大最终缩放（2.5→3.0） */
  }
}

// 关键修改：统一爆炸动画
@keyframes explode {
  0% {
    background-position:
      50% 50%,
      50% 50%,
      // 中心实虚圈
      50% 20%,
      50% 80%,
      20% 50%,
      80% 50%,
      35% 35%,
      65% 35%,
      35% 65%,
      65% 65%,
      // 实圈初始位置
      50% 20%,
      50% 80%,
      20% 50%,
      80% 50%,
      35% 35%,
      65% 35%,
      35% 65%,
      65% 65%; // 虚圈初始位置
    background-size: 18% 18%;
    opacity: 1;
  }
  100% {
    background-position:
      50% 50%,
      50% 50%,
      // 中心保持
      50% -20%,
      50% 120%,
      -20% 50%,
      120% 50%,
      10% 10%,
      90% 10%,
      10% 90%,
      90% 90%,
      // 实圈终点（更分散）
      50% -20%,
      50% 120%,
      -20% 50%,
      120% 50%,
      10% 10%,
      90% 10%,
      10% 90%,
      90% 90%; // 虚圈终点同步
    background-size: 0% 0%;
    opacity: 0;
  }
}
</style>
