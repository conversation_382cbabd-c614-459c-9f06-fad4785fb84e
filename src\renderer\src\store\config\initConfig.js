import axios from 'axios'
import {startHeart, endHeart} from './heartbeat'
import configManager from '../../utils/configManager'
import { ElMessage, ElMessageBox } from 'element-plus'

// 版本比较函数
const compareVersions = (version1, version2) => {
    if (!version1 || !version2) return 0

    // 移除 'v' 前缀并分割版本号
    const v1 = version1.replace(/^v/, '').split('.').map(Number)
    const v2 = version2.replace(/^v/, '').split('.').map(Number)

    // 补齐版本号长度
    const maxLength = Math.max(v1.length, v2.length)
    while (v1.length < maxLength) v1.push(0)
    while (v2.length < maxLength) v2.push(0)

    // 逐位比较
    for (let i = 0; i < maxLength; i++) {
        if (v1[i] > v2[i]) return 1
        if (v1[i] < v2[i]) return -1
    }

    return 0
}

export let init = async e=>{
    startHeart()

    try {
        // 首先尝试加载本地配置
        const hasLocalConfig = await configManager.initialize()
        console.log('hasLocalConfig', hasLocalConfig)

        let localVersion = null
        let localConfig = null

        if (hasLocalConfig) {
            localConfig = configManager.getCurrentConfig()
            localVersion = configManager.getCurrentVersion()
            console.log('本地配置版本:', localVersion)
        }

        // 无论是否有本地配置，都检查API版本
        console.log('检查API配置版本...')
        let apiResponse = null
        let apiVersion = null

        try {
            apiResponse = await axios.get('/api/v1.0/device/config')
            if (apiResponse.data && apiResponse.data.data && apiResponse.data.data.config_version) {
                apiVersion = apiResponse.data.data.config_version
                window.apiVersion = apiVersion
                console.log('API配置版本:', apiVersion)
            }
        } catch (apiError) {
            console.warn('API请求失败:', apiError.message)

            // API请求失败，如果有本地配置就使用本地配置
            if (hasLocalConfig && localConfig) {
                // ElMessage.warning('网络连接失败，使用本地缓存配置')
                return localConfig
            } else {
                // throw new Error('无法获取配置：网络连接失败且无本地缓存')
            }
        }

        // 比较版本
        if (hasLocalConfig && localVersion && apiVersion) {
            const versionComparison = compareVersions(apiVersion, localVersion)

            if (versionComparison > 0) {
                // API版本更新，提示用户手动更新
                console.log(`发现新版本: ${apiVersion} > ${localVersion}`)

                try {
                    await ElMessageBox.confirm(
                        `发现新的配置版本 ${apiVersion}，当前本地版本为 ${localVersion}。\n是否立即更新到最新版本？`,
                        '配置更新提醒',
                        {
                            confirmButtonText: '立即更新',
                            cancelButtonText: '稍后更新',
                            type: 'info',
                            distinguishCancelAndClose: true
                        }
                    )

                    // 用户确认更新
                    ElMessage.info('正在下载最新配置...')
                    const downloadResult = await configManager.downloadConfigResources(apiResponse.data.data)

                    if (downloadResult.success) {
                        ElMessage.success(`配置已更新到版本 ${apiVersion}`)
                        return configManager.getCurrentConfig()
                    } else {
                        ElMessage.error('配置更新失败，继续使用本地版本')
                        return localConfig
                    }

                } catch (error) {
                    if (error === 'cancel' || error === 'close') {
                        console.log('用户选择稍后更新，继续使用本地配置')
                        ElMessage.info('继续使用本地配置，您可以稍后手动更新')
                        return localConfig
                    } else {
                        console.error('更新过程中发生错误:', error)
                        ElMessage.error('更新失败，继续使用本地配置')
                        return localConfig
                    }
                }

            } else if (versionComparison === 0) {
                // 版本相同，使用本地配置
                console.log('本地配置版本与API版本一致，使用本地配置')
                return localConfig

            } else {
                // 本地版本更新（开发环境可能出现）
                console.log('本地配置版本比API版本更新，使用本地配置')
                return localConfig
            }
        } else if (hasLocalConfig && localConfig) {
            // 有本地配置但无法获取API版本信息，使用本地配置
            console.log('无法获取API版本信息，使用本地配置')
            return localConfig

        } else if (apiResponse && apiResponse.data && apiResponse.data.data) {
            // 没有本地配置，首次使用，询问是否下载
            console.log('首次使用，从API获取配置')

            try {
                await ElMessageBox.confirm(
                    `欢迎使用！检测到配置版本 ${apiVersion}，是否下载配置资源到本地？\n这样可以提高加载速度并支持离线使用。`,
                    '初始化配置',
                    {
                        confirmButtonText: '下载到本地',
                        cancelButtonText: '仅在线使用',
                        type: 'info'
                    }
                )

                // 用户确认下载
                const downloadResult = await configManager.downloadConfigResources(apiResponse.data.data)

                if (downloadResult.success) {
                    console.log('配置资源下载完成，使用本地配置')
                    return configManager.getCurrentConfig()
                } else {
                    console.warn('配置资源下载失败，使用在线配置')
                    return apiResponse.data
                }

            } catch (error) {
                // 用户取消下载
                console.log('用户选择仅在线使用')
                return apiResponse.data
            }
        }

        // 兜底情况
        console.log('使用API响应数据')
        return apiResponse?.data || null

    } catch (error) {
        console.error('配置初始化失败:', error)

        // 最后的兜底：尝试使用任何可用的本地配置
        const localConfig = configManager.getCurrentConfig()
        if (localConfig) {
            ElMessage.warning('初始化失败，使用本地缓存配置')
            return localConfig
        }

        ElMessage.error('配置初始化失败，请检查网络连接')
        throw error
    }
}