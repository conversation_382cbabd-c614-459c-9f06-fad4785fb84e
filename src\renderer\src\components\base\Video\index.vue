<template>
  <video v-show="videoShow" :style="extraStyle" ref="videoRef" width="100%" height="100%" autoplay playsinline></video>
</template>

<script setup>
import { useCameraStore } from '@/store/camera'
import {ref,onMounted,onUnmounted} from 'vue'
import bus from '@/utils/bus'
const cameraStore = useCameraStore();
const myEmits = defineEmits(['initFinished'])
const myProps = defineProps({
    extraStyle:{
        type:Object,
        default:()=>{
            return {

            }
        }
    },
    cameraType:{
        type:String,
        default:'common'
    },
    isSnapNeedReLink:{
      type:Boolean,
      default:false
    }
  }
)
const videoRef = ref(null)
const cameraTypeObj = {
  common: {
    variable: 'cameraCommonStream',
    loadFn: 'loadCommonStream',
    setFn: 'setCommonStream'
  },
  snap: {
    variable: 'cameraSnapStream',
    loadFn: 'loadSnapStream',
    setFn: 'setSnapStream'
  }
}
const videoShow = ref(true)
const initCamera = async(isNeed)=>{
  if(cameraStore[cameraTypeObj[myProps.cameraType].variable]){
    videoRef.value.srcObject = cameraStore[cameraTypeObj[myProps.cameraType].variable];
   !isNeed &&  myEmits('initFinished',videoRef.value)
  }else {
    const stream = await cameraStore[cameraTypeObj[myProps.cameraType].loadFn]();
        if (stream) {
          // 存放在store中
          cameraStore[cameraTypeObj[myProps.cameraType].setFn](stream)
          videoRef.value.srcObject = stream;
         !isNeed && myEmits('initFinished',videoRef.value)
        }
  }
}
onMounted(() => {
  console.log('渲染2')
  initCamera()
  bus.on('reLinkError', () => {
    if (myProps.cameraType == 'common') {
      videoShow.value = false
    }
  })
  bus.on('reLinkSuccess', async(stream) => {
    if(myProps.isSnapNeedReLink){
        console.log('需要')
        await initCamera(true);
        bus.emit('cameraInitFinished')
       console.log('重置相机')
       return false;
    }
    if (myProps.cameraType == 'common') {
      videoShow.value = true
      videoRef.value.srcObject = stream
      bus.emit('cameraInitFinished')
    }
  })
})
onUnmounted(() => {
  bus.off('reLinkError')
  bus.off('reLinkSuccess')
})
</script>

<style lang="less" scoped>
video {
  object-fit: cover; /* 填充容器 */
  background: #000; /* 黑底占位 */
}
</style>
