import { createRouter, createWebHashHistory } from "vue-router";
import home from '@/views/popfifiConfig/home.vue'
import capture from '@/views/popfifiConfig/capture.vue'
import themeSelect from '@/views/popfifiConfig/themeSelect.vue'
import loading from '@/views/popfifiConfig/loading.vue'
import result from '@/views/popfifiConfig/result.vue'
import Login from "@/views/popfifiConfig/login.vue";
import { compile } from "vue";
import component from "element-plus/es/components/tree-select/src/tree-select-option.mjs";
import welcome from '@/views/popfifiConfig/welcome.vue'
import TakePhoto9_9 from "@/views/popfifiConfig/9_9Page/index.vue"
import FamilyHome from "@/views/popfifiConfig/family/index.vue"
import familyLoading from "@/views/popfifiConfig/family/generating.vue";
import familyResult from "@/views/popfifiConfig/family/result.vue";
import testImg from '@/views/popfifiConfig/result_makeImgtemp.vue';
import xingyun from '@/activityPage/xingyunshike.vue'
import path from "path";
const routes = [
  {
    path: '/xingyun',
    component: xingyun
  },
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/welcome",
    name: "welcome",
    component: welcome,
  },
  {
   path: "/takePhoto9_9",
   name: "takePhoto9_9",
   component: TakePhoto9_9,
   meta:{
			title:"9.9活动",
			// 路由动画
			transition:'animate__fadeIn'
		}
  },
  {
    path: "/login",
    component: Login,
  },
  {
    path: "/home_capture",
    name: "home_capture",
    component: home,
  },
  {
    path: "/rebuild2-themeSelect",
    name: "rebuild2-themeSelect",
    component: themeSelect
  },
  // {
  //   path: "/rebuild2-capture",
  //   name: "rebuild2-capture",
  //   component: capture,
  // },
  {
    path: "/rebuild2-loading",
    name: "rebuild2-loading",
    component: loading,
  },
  {
    path: "/rebuild2-result",
    name: "rebuild2-result",
    component: result,
  },
  {
    path:'/family-home',
    name:'family-home',
    component:FamilyHome,
  },
  {
    path: '/family-result',
    name: 'family-result',
    component: familyResult,
  },
  {
    path: '/family-loading',
    name: 'family-loading',
    component: familyLoading,
  },
  {
    path: '/testImg',
    name: 'testImg',
    component: testImg,
  }
  
];
// console.log(VueRouter);
export default createRouter({
  // 4. Provide the history implementation to use. We are using the hash history for simplicity here.
  history: createWebHashHistory(),
  routes, // short for `routes: routes`
});
