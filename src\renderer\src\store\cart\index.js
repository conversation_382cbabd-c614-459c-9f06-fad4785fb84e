import {ref,computed} from 'vue'
import { defineStore } from 'pinia'

const cartList = ref([])

const favorList = ref([])

const payCodeUrl = ref('');
const businessOrderId = ref('');

const setCartList = (data) => {
    cartList.value = data;
}

const setFavorList = (data)=>{
    favorList.value = data
}

const setPayCodeUrl = (data) => {
    payCodeUrl.value = data;
}

const setBusinessOrderId = (data) => {
    businessOrderId.value = data;
}

// 购物车相关
export const useCartStore = defineStore('cartStore',()=>{
    return { cartList,favorList,payCodeUrl,businessOrderId,setBusinessOrderId,setCartList,setFavorList,setPayCodeUrl }
})