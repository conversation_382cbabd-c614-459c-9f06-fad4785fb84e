<template>
  <!-- 活动页面的公共底部 -->
  <div :class="['active-bottom']" 
  :style="{
     background: myProps.themeImg.bgImg?`url(${myProps.themeImg.bgImg}) no-repeat center center`:'none',
     backgroundSize:myProps.themeImg.bgImg?'cover':'initial'
     }">
    <div 
     class="border-wraper" 
     :style="{
     background: myProps.themeImg.outerCircleBgImg?`url(${myProps.themeImg.outerCircleBgImg}) no-repeat center center`:'none',
      backgroundSize:myProps.themeImg.outerCircleBgImg?'contain':'initial'
     }" 
    >
      <div class="left">
        <div class="btns">
          <div
            v-for="btnItem in btnsArr"
            :key="btnItem.type"
            :class="[
              'btns-item',
              {
                'is-small': btnItem.isSmall
              }
            ]"
            @click="btnItem.handle"
            :style="{
              background: `url(${btnItem.icon}) no-repeat center center`,
              backgroundSize: 'cover',
              display: myProps[btnItem.type] ? 'flex' : 'none',
              width: btnItem.width,
              height: btnItem.width
            }"
          >
            <div v-if="!myProps[btnItem.loadingType]" class="btns-item-text">
              <!-- <span class="title1">{{ btnItem.title }}</span> -->
              <img :src="btnItem.textIcon" class="text-img" />
            </div>
            <base-loading v-if="btnItem.loadingType && myProps[btnItem.loadingType]"></base-loading>
          </div>
        </div>
      </div>
      <span class="text">1</span>
      <div class="right">
        <div class="video-content">
          <div class="guang"></div>
          <div class="my-bottom-video">
            <div class="my-video-content">
              <video
                src="@/assets/video/480.mp4"
                class="viewVideo"
                autoplay
                loop
                playsinline
                muted
                ref="viewVideo"
              ></video>
            </div>
          </div>
          <div class="pay-info" v-if="isShowCenterBox">
            <template v-if="!showGenrateLoading">
              <div class="code-content">
                <base-er-code
                  :codeData="codeData"
                  imgWidth="240rem"
                  imgHeight="240rem"
                  :isPaySuccess="isPaySuccess"
                ></base-er-code>
              </div>
              <div class="desc">
                <template v-if="!isPaySuccess">
                  <div class="text1">限时特惠：{{ formatPrice(goodPrice) }}元</div>
                  <div class="text2">
                    请使用<span class="wx">微信</span>或<span class="ali">支付宝</span>扫码支付
                  </div>
                </template>
                <template v-else>
                  <div class="text1">支付成功</div>
                </template>
              </div>
            </template>
            <template v-else>
              <base-loading></base-loading>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import BaseErCode from '@/components/base/ErCode/index.vue'
import BaseLoading from '@/components/base/Loading/index.vue'
import { formatPrice } from '@/utils'
import bus from '@/utils/bus'
import { getAllGoodsList, createOrder, getOrderStatus } from '@/apis/9_9'
import { ElMessage } from 'element-plus'
import { ref, onMounted, onUnmounted } from 'vue'
import reTake from '@/assets/bottom/re-take.png'
import reTakeText from '@/assets/bottom/re-take-text.png'
import sure from '@/assets/bottom/sure.png'
import sureText from '@/assets/bottom/sure-text.png'
import back from '@/assets/bottom/back.png'
import backText from '@/assets/bottom/back-text.png'
import purchaseText from '@/assets/bottom/purchase-text.png'
import bottomBtnBg from '@/assets/yinyuejie/welcome/bottom-btn-bg.png'
// 定义组件触发的事件
const myEmits = defineEmits([
  'handleCreateOrder',
  'handleRetake',
  'handleBack',
  'handleSure',
  'countFinished',
  'countDecrease'
])
const myProps = defineProps({
  // 需要购买的商品的codenum
  goodCode: {
    type: String,
    default: 'V1_TICKED_OFF'
  },
  // 倒计时多少秒
  countdown: {
    type: Number,
    default: 0
  },
  // 是否显示购买按钮
  isHasBuyBtn: {
    type: Boolean,
    default: true
  },
  // 是否显示确认按钮
  isHasSureBtn: {
    type: Boolean,
    default: false
  },
  // 是否显示重拍按钮
  isHasRetakeBtn: {
    type: Boolean,
    default: true
  },
  // 是否显示返回按钮
  isHasBackBtn: {
    type: Boolean,
    default: true
  },
  // 是否具有重拍功能，没有重拍功能，但有重拍按钮的页面使用
  isHasRetakeFn: {
    type: Boolean,
    default: true
  },
  buyLoading: {
    type: Boolean,
    default: false
  },
  sureLoading: {
    type: Boolean,
    default: false
  },
  themeImg:{
    type:Object,
    default:{
      bgImg:'',
      outerCircleBgImg:'',
      btnImg:'',
      sureBtnTextImg:'',
      backBtnTextImg:'',
      purchaseTextImg:'',
      reTakeBtnTextImg:''
    }
  }
})
const viewVideo = ref(null)
const currentGoodItem = ref({}) // 当前商品信息
const isCapturePhotoFinished = ref(false)
const timer = ref(null) // 定时器
const setTimer = ref(null)
const isShowCenterBox = ref(false) // 是否显示中间的盒子
const showGenrateLoading = ref(false)
const isPaySuccess = ref(false) // 是否支付成功
const codeData = ref('') // 二维码数据
const businessOrderId = ref('') // 订单id
const goodPrice = ref(0) // 商品价格
const myCountdown = ref(0) // 倒计时
const initTimmerAndLoading = () => {
  clearInterval(timer.value)
  timer.value = null
  clearTimeout(setTimer.value)
  setTimer.value = null
  isPaySuccess.value = false
}
const initTakePhoto = () => {
  clearInterval(timer.value)
  timer.value = null
  clearTimeout(setTimer.value)
  setTimer.value = null
  isShowCenterBox.value = false
  showGenrateLoading.value = false
  isPaySuccess.value = false
  myCountdown.value = myProps.countdown
  isCapturePhotoFinished.value = false
  myEmits('countDecrease', myProps.countdown)
}
const startCountdown = () => {
  timer.value = setInterval(() => {
    if (myCountdown.value > 0) {
      myCountdown.value--
      myEmits('countDecrease', myCountdown.value)
    } else {
      clearInterval(timer.value)
      timer.value = null
      myEmits('countFinished')
    }
  }, 1000)
}

const handleClickBuy = async () => {
  if (myCountdown.value != 0) {
    ElMessage({
      message: '请先拍照',
      type: 'error'
    })
    return false
  }
  if (!isCapturePhotoFinished.value) {
    ElMessage({
      message: '正在拍照中，请稍等...',
      type: 'error'
    })
    return false
  }
  if (isShowCenterBox.value && !isPaySuccess.value) {
    ElMessage({
      message: '订单正在支付中，请勿重复下单！',
      type: 'error'
    })
    return false
  }
  initTimmerAndLoading()
  isShowCenterBox.value = true
  showGenrateLoading.value = true
  const res = await getAllGoodsList()
  if (res && res.code == 0) {
    const findItem = res.data.goods_list.find((item) => item.number == myProps.goodCode)
    currentGoodItem.value = findItem
    myEmits('handleCreateOrder', currentGoodItem.value)
  }
}
const handleClickRetake = () => {
  myEmits('handleRetake')
  if (myProps.isHasRetakeFn) {
    initTakePhoto()
    startCountdown()
  }
}
const handleClickBack = () => {
  myEmits('handleBack')
}
const handleClickSure = () => {
  myEmits('handleSure')
}
const handleListener = () => {
  bus.on('orderData', async (orderData) => {
    // 收到创建订单的数据，开始创建订单
    // 开始组装数据
    console.log('收到了么')
    const res = await createOrder(orderData)
    showGenrateLoading.value = false
    if (res && res.code == 0) {
      codeData.value = res.data.qrcode_url
      businessOrderId.value = res.data.business_order_id
      goodPrice.value = res.data.payment_amount
      handleQueryPayStatus()
    }
  })
  bus.on('cameraInitFinished', () => {
    console.log('收到消息')
    handleClickRetake()
  })
  bus.on('cameraCaptureFinished', () => {
    isCapturePhotoFinished.value = true
  })
  
}

const handleDestroyListener = () => {
  bus.off('orderData')
  bus.off('cameraInitFinished')
  bus.off('cameraCaptureFinished')
}

// 请求接口查询支付状态
const handleQueryPayStatus = async () => {
  try {
    const res = await getOrderStatus({
      business_order_id: businessOrderId.value
    })
    if (res?.code == '0') {
      const status = res.data.pay_status
      // 支付完成状态
      if ([1, 2].includes(status)) {
        isPaySuccess.value = true
        clearTimeout(setTimer.value)
        setTimer.value = null
        return
      }
      // 支付中状态时继续轮询
      if (status == 0) {
        setTimer.value = setTimeout(() => {
          handleQueryPayStatus() // 1秒后发起下一次请求
        }, 1000)
      }
    }
  } catch (error) {
    console.error('请求失败:', error)
    ElMessage({
      message: '请求失败:',
      error,
      type: 'error'
    })
    clearTimeout(setTimer.value)
    setTimer.value = null
  }
}

const btnsArr = ref([
  {
    title: '重拍',
    bgc: '#808080',
    color: '#fff',
    type: 'isHasRetakeBtn',
    width: '230rem',
    icon:myProps.themeImg.btnImg,
    textIcon: myProps.themeImg.reTakeBtnTextImg,
    isSmall: true,
    handle: handleClickRetake
  },
  {
    title: '购买',
    bgc: '#7948EA',
    color: '#fff',
    type: 'isHasBuyBtn',
    width: '300rem',
    isSmall: false,
    icon: myProps.themeImg.btnImg,
    textIcon: myProps.themeImg.purchaseTextImg,
    loadingType: 'buyLoading',
    handle: handleClickBuy
  },
  {
    title: '确认',
    bgc: '#7948EA',
    color: '#fff',
    type: 'isHasSureBtn',
    width: '300rem',
    isSmall: false,
    icon: myProps.themeImg.btnImg,
    textIcon: myProps.themeImg.sureBtnTextImg,
    loadingType: 'sureLoading',
    handle: handleClickSure
  },

  {
    title: '返回',
    bgc: '#E3E3E3',
    color: '#000',
    type: 'isHasBackBtn',
    width: '230rem',
    isSmall: true,
    icon: myProps.themeImg.btnImg,
    textIcon: myProps.themeImg.backBtnTextImg,
    handle: handleClickBack
  }
])
onMounted(() => {
  myCountdown.value = myProps.countdown
  if (myProps.countdown == 0) {
    isCapturePhotoFinished.value = true
  }
  console.log('渲染3')
  handleListener()
})
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  if (setTimer.value) {
    clearTimeout(setTimer.value)
    setTimer.value = null
  }
  handleDestroyListener()
})
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'DouyinSansBold';
  src: url('@/assets/fonts/DouyinSansBold.ttf') format('truetype');
}
@property --angle {
  syntax: '<angle>';
  inherits: false;
  initial-value: 0deg;
}
@keyframes rotate {
  to {
    --angle: 360deg;
  }
}
.wx {
  color: #15ff0d;
}
.ali {
  color: rgba(42, 130, 228, 1);
}
.active-bottom {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'DouyinSansBold', sans-serif;
  // background: url('@/assets/welcome/images/welcome-bottom.png') no-repeat center center;
  background-size: cover;
  .border-wraper {
    width: 98%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding: 0rem 40rem 0rem 40rem;
    box-sizing: border-box;
    align-items: center;
    .text {
      color: rgba(0, 0, 0, 0);
      font-size: 2px;
    }
    .left {
      height: 100%;
      flex: 1;
      .btns {
        height: 100%;
        display: flex;
        // flex-direction: column;
        justify-content: center;
        align-items: center;
        justify-content: space-around;
        &-item {
          width: 173rem;
          height: 173rem;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;

          &-text {
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
            .text-img {
              width: auto;
              height: 80rem;
            }
          }
        }
        .is-small {
          .btns-item-text {
            .text-img {
              width: auto;
              height: 60rem;
            }
          }
        }
      }
    }
    .right {
      width: 430rem;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .video-content {
        width: 380rem;
        height: 380rem;
        box-sizing: border-box;
        border-radius: 17rem;
        filter: drop-shadow(0 0 10rem hsl(162, 100%, 58%)) drop-shadow(0 0 20rem hsl(270 63% 58%));
        position: relative;
        .guang {
          width: 100%;
          height: 100%;
          border-radius: 17rem;
          clip-path: polygon(
            0% 0%,
            /* 外框左上 */ 100% 0%,
            /* 外框右上 */ 100% 100%,
            /* 外框右下 */ 0% 100%,
            /* 外框左下 */ 0% 0%,

            /* 闭合外框 */ /* 内框裁剪（从10%到90%） */ 3% 3%,
            /* 内框左上 */ 3% 97%,
            /* 内框左下 */ 97% 97%,
            /* 内框右下 */ 97% 3%,
            /* 内框右上 */ 3% 3% /* 闭合内框 */
          );
          background: conic-gradient(
            from var(--angle),
            hsl(162, 100%, 58%),
            hsl(270, 63%, 58%),
            hsl(162, 100%, 58%)
          );
          animation: rotate 3s infinite linear;
        }
        .my-bottom-video {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: 998;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 17rem;
          .my-video-content {
            width: 94%;
            height: 94%;
            border-radius: 8rem;
            overflow: hidden;
            video {
              width: 100%;
              height: 100%;
              // transform: translateZ(0); // 触发GPU加速
              // will-change: transform;   // 提示浏览器优化
            }
          }
        }

        .pay-info {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 19rem;
          background: rgba(43, 36, 36, 0.8);
          z-index: 999;
          .code-content {
            width: 100%;
          }
          .desc {
            font-size: 24rem;
            font-weight: 700;
            color: #fff;
            display: flex;
            flex-direction: column;
            gap: 3rem;
            .text1 {
            }
            .text2 {
            }
          }
        }
      }
    }
  }
}


</style>
