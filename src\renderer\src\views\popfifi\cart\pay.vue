<template>
  <div class="cart-pay">
    <div class="top">
      <div class="top-img">
        <img class="my-img" src="@/assets/cart/pay/title.png" alt="" />
      </div>
    </div>
    <!-- 未支付成功 -->
    <template v-if="!isPaySuccess">
      <div class="bottom">
        <div class="code-box">
          <img class="bg" src="@/assets/cart/pay/border.png" alt="" />
          <div class="code-content">
            <base-er-code
              :codeData="cartStore?.payCodeUrl"
              imgWidth="618rem"
              imgHeight="618rem"
              :isPaySuccess="isCodeShow"
              :payImg="paySuccessImg"
            ></base-er-code>
          </div>
        </div>
        <div class="tips">
          <img class="pay-tips-img" src="@/assets/cart/pay/pay-tips.png" alt="" />
        </div>
        <div class="back" @click="handleClickBackCart">返回选择更多内容</div>
      </div>
    </template>
    <!-- 支付成功 -->
    <template v-else>
      <div class="success-bottom">
        <div class="tips">已支付，产品制作中</div>
        <div class="box-item">
          <div class="code-content">
            <img class="bg" src="@/assets/cart/pay/border.png" alt="" />
            <div class="inner-content">
              <img class="inner-img" src="@/assets/cart/pay/nfc.png" alt="" />
            </div>
          </div>
          <div class="miaoshu">手机轻触或微信扫码，激活数字分身</div>
        </div>
        <div class="box-item" style="margin-top: 120rem">
          <div class="code-content">
            <img class="bg" src="@/assets/cart/pay/border.png" alt="" />
            <div class="inner-content">
              <img class="inner-img" src="@/assets/cart/pay/gongzhong.png" alt="" />
            </div>
          </div>
          <div class="miaoshu">关注解锁更多玩法</div>
        </div>
        <div class="back" @click="handleClickBackHome">
          {{ countNum }}<span class="small">s</span>后自动返回首页
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import BaseErCode from '@/components/base/ErCode/index.vue'
import paySuccessImg from '@/assets/cart/pay/success.png'
import { useCartStore } from '@/store/cart'
import { getOrderStatus } from '@/apis/9_9'
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
const cartStore = useCartStore()
const router = useRouter()
const isPaySuccess = ref(false)
const isCodeShow = ref(false)
const countNum = ref(30)
const finTimmer = ref(null)
const setTimer = ref(null)

const startFinTimmer = () => {
  finTimmer.value = setInterval(() => {
    countNum.value--
    if (countNum.value <= 0) {
      clearInterval(finTimmer.value)
      finTimmer.value = null
      handleClickBackHome()
    }
  }, 1000)
}

// 请求接口查询支付状态
const handleQueryPayStatus = async () => {
  try {
    const res = await getOrderStatus({
      business_order_id: cartStore.businessOrderId
    })
    if (res?.code == '0') {
      const status = res.data.pay_status
      // 支付完成状态
      if ([1, 2].includes(status)) {
        isCodeShow.value = true
        setTimeout(() => {
          isPaySuccess.value = true
          startFinTimmer()
        }, 1000)
        clearTimeout(setTimer.value)
        setTimer.value = null
        return
      }
      // 支付中状态时继续轮询
      if (status == 0) {
        setTimer.value = setTimeout(() => {
          handleQueryPayStatus() // 1秒后发起下一次请求
        }, 1000)
      }
    }
  } catch (error) {
    console.error('请求失败:', error)
    ElMessage({
      message: '请求失败:',
      error,
      type: 'error'
    })
    clearTimeout(setTimer.value)
    setTimer.value = null
  }
}

const handleClickBackHome = () => {
  cartStore.setFavorList([])
  cartStore.setCartList([])
  cartStore.setPayCodeUrl('')
  cartStore.setBusinessOrderId('')

  router.push({ path: '/welcome' })
}

const handleClickBackCart = () => {
  router.push({
    path: '/cart',
    query: {
      isFromCartPay: true
    }
  })
}

onMounted(() => {
  if (cartStore?.payCodeUrl) {
    handleQueryPayStatus()
  }
})

onUnmounted(() => {
  if (setTimer.value) {
    clearTimeout(setTimer.value)
    setTimer.value = null
  }
  if (finTimmer.value) {
    clearInterval(finTimmer.value)
    finTimmer.value = null
  }
})
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'MyCartFont';
  src: url('@/assets/fonts/HaiPaiQiangDiaoGunShiJian-2.otf') format('truetype');
}
.small {
  font-size: 40rem;
  padding: 0 6rem;
}
// 抽离公共的样式代码，可以传颜色的值,和阴影的值
.commonStyle(@c,@s) {
  font-size: 54.66rem;
  color: @c;
  text-shadow: 5rem 5.66rem 13.33rem @s;
  text-align: center;
}
.cart-pay {
  width: 100%;
  height: 100%;
  background: url('@/assets/cart/pay/bg.png') no-repeat center center; // 新增背景图
  background-size: cover; // 保持背景图覆盖
  display: flex;
  flex-direction: column;
  font-family: 'MyCartFont', sans-serif;
  .top {
    width: 100%;
    display: flex;
    justify-content: center;
    .top-img {
      padding: 64rem 16% 0 16%;
      box-sizing: border-box;
      .my-img {
        width: 100%;
        height: auto;
      }
    }
  }
  .bottom {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    .code-box {
      width: 50%;
      position: relative;
      margin-top: 25%;
      .bg {
        width: 100%;
        height: auto;
      }
      .code-content {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        padding: 46.6rem 37.3rem;
        box-sizing: border-box;
      }
    }
    .tips {
      padding-top: 28rem;
      .pay-tips-img {
        width: 466.66rem;
        height: auto;
      }
    }
    .back {
      position: absolute;
      bottom: 9.2%;
      left: 0;
      width: 100%;
      .commonStyle(#00ffb4,#a81a69);
    }
  }
  .success-bottom {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    .tips {
      padding: 166rem 0 106.6rem 0;
      box-sizing: border-box;
      .commonStyle(#25d9a4,#a81a69);
      text-stroke: 2px #ffffff;
      -webkit-text-stroke: 2px #ffffff;
    }
    .box-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .code-content {
        width: 38%;
        position: relative;
        .bg {
          width: 100%;
          height: auto;
        }
        .inner-content {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          box-sizing: border-box;
          padding: 36.66rem 28.66rem;
          display: flex;
          align-items: center;
          .inner-img {
            width: 100%;
            height: auto;
          }
        }
      }
      .miaoshu {
        padding-top: 10rem;
        .commonStyle(#fde5e5,#a81a69);
      }
    }
    .back {
      position: absolute;
      bottom: 4.2%;
      left: 0;
      width: 100%;
      .commonStyle(#00ffb4,#a81a69);
    }
  }
}
</style>
