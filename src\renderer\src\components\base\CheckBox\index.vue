<template>
  <div class="my-checkbox">
    <div
      class="my-checkbox__box"
      :class="{ 'my-checkbox__box--active': showDialog }"
      :style="{
        backgroundImage: `url(${myProps?.checkImg})`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center center'
      }"
      @click.stop="showDialog = !showDialog"
    ></div>
    <slot></slot>
  </div>
</template>

<script setup>
import checkBgImg from '@/assets/yinyuejie/welcome/check.png'
import { ref } from 'vue'
const showDialog = defineModel({ default: false })
defineOptions({
  inheritAttrs: false
})
const myProps = defineProps({
  checkImg: {
    type: String,
    default: checkBgImg
  }
})
</script>

<style lang="less" scoped>
@mycheckbox: rgba(250, 141, 229, 1);

.my-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 10rem;

  &__box {
    width: 50rem;
    height: 50rem;
    position: relative;
    margin-right: 10rem;
    transition: all 0.2s;
    box-sizing: border-box;
    overflow: hidden; // 新增overflow

    background-size: cover;
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      width: 26rem;
      height: 26rem;
      background: @mycheckbox;
      border-radius: 50%;
      transition: all 0.2s;
      z-index: 1;
    }
    &--active {
      border-color: @mycheckbox;
      &::after {
        transform: translate(-50%, -50%) scale(1);
      }
    }
  }
}
</style>
