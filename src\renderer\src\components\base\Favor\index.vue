<template>
  <div class="favor">
    <!-- From Uiverse.io by catraco -->
    <div
      title="Like"
      :class="[
        'heart-container',
        {
          'animate-heart': isHeartAnimate
        }
      ]"
      :style="{
        width:myProps.size,
        height:myProps.size,
        '--heart-color': myProps.color,
         '--heart-color-rgb': hexToRgb(myProps.color)
      }"
      @click="handleClickFavor"
    >
      <div class="svg-container">
        <svg xmlns="http://www.w3.org/2000/svg" class="svg-outline" viewBox="0 0 24 24">
          <path
            d="M17.5,1.917a6.4,6.4,0,0,0-5.5,3.3,6.4,6.4,0,0,0-5.5-3.3A6.8,6.8,0,0,0,0,8.967c0,4.547,4.786,9.513,8.8,12.88a4.974,4.974,0,0,0,6.4,0C19.214,18.48,24,13.514,24,8.967A6.8,6.8,0,0,0,17.5,1.917Zm-3.585,18.4a2.973,2.973,0,0,1-3.83,0C4.947,16.006,2,11.87,2,8.967a4.8,4.8,0,0,1,4.5-5.05A4.8,4.8,0,0,1,11,8.967a1,1,0,0,0,2,0,4.8,4.8,0,0,1,4.5-5.05A4.8,4.8,0,0,1,22,8.967C22,11.87,19.053,16.006,13.915,20.313Z"
          ></path>
        </svg>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          :class="[
            'svg-filled',
            {
              'to-fill': isFavor
            }
          ]"
          viewBox="0 0 24 24"
        >
          <path
            d="M17.5,1.917a6.4,6.4,0,0,0-5.5,3.3,6.4,6.4,0,0,0-5.5-3.3A6.8,6.8,0,0,0,0,8.967c0,4.547,4.786,9.513,8.8,12.88a4.974,4.974,0,0,0,6.4,0C19.214,18.48,24,13.514,24,8.967A6.8,6.8,0,0,0,17.5,1.917Z"
          ></path>
        </svg>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted,nextTick  } from 'vue'
 const myProps = defineProps({
    size:{
        type:String,
        default:'200rem'
    },
    color:{
        type:String,
        default:'#ec3873'
    }
 })
const myEmits = defineEmits(['change'])
const isFavor = defineModel({ default: false })
const isHeartAnimate = ref(false) // 新增爱心动画状态
const isAnimate = ref(false)
const handleClickFavor = async() => {
  console.log(isFavor.value,'原始的值')
  isFavor.value = !isFavor.value
//   改变isFavor的值
 // 等待 Vue 异步更新完成
  await nextTick();
  myEmits('change')
  console.log(isFavor.value,'改变之后的值')
  if (isFavor.value) {
    console.log('开始没有？')
    isHeartAnimate.value = true
    setTimeout(() => {
      isHeartAnimate.value = false
    }, 1500)
  } else {
    isHeartAnimate.value = false
  }
}
const hexToRgb = (hex) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `${r}, ${g}, ${b}`;
};
const handleClick = () => {
  isAnimate.value = false
  isAnimate.value = true
  setTimeout(() => {
    isAnimate.value = false
  }, 700)
}
defineExpose({
   handleClickFavor
});
</script>

<style lang="less" scoped>
.favor {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
/* From Uiverse.io by catraco */
.heart-container {
  position: relative;

  overflow: visible; // 新增允许内容溢出
  transition: 0.3s;
  .svg-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative; // 确保SVG定位基准
    /* 新增射线动画 */
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1; /* 确保在爱心上方 */
  }

  /* 创建8条不同角度的射线 */
  &::after {
       content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
     background: repeating-conic-gradient(
        transparent 0deg,
        transparent calc(360deg/8 - 1deg),  /* 关键修改：缩小角度差（5°→3°），线条更细 */
        var(--heart-color) calc(360deg/8 - 1deg),
        var(--heart-color) calc(360deg/8),
        transparent calc(360deg/8)
      );
      /* 关键修改：调整mask范围，让射线从爱心外部开始 */
      mask: radial-gradient(
        circle,
        transparent 80%,  /* 中心80%区域隐藏（爱心占据约80%宽度） */
        white 80%  /* 80%到100%区域显示射线 */
      );
      /* 关键修改：初始缩放值调整为0.8（对应mask的80%位置） */
      opacity: 0;
      transform: scale(0.8);  /* 从爱心边缘外开始 */
    }
  }

  .svg-outline {
    fill: var(--heart-color);
    position: absolute;
  }
  .svg-filled {
    fill: var(--heart-color);
    position: absolute;
    animation: keyframes-svg-filled 1s;
    display: none;
  }

  .to-fill {
    display: block;
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    width: 400%; // 增大覆盖范围
    height: 400%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%); // 精确中心定位
    z-index: 1;
    background-repeat: no-repeat;
    display: none;
  }

  &.animate-heart {
    // 顶部气泡组
    &:before {
      display: block;
      animation: explode 0.8s ease-out forwards;
      background-image:
    // 虚圈（空心圆环，内径5%，外径6%，形成细环）
        radial-gradient(circle at 50% 20%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        radial-gradient(circle at 50% 80%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        radial-gradient(circle at 20% 50%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        radial-gradient(circle at 80% 50%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        radial-gradient(circle at 35% 35%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        radial-gradient(circle at 65% 35%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        radial-gradient(circle at 35% 65%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        radial-gradient(circle at 65% 65%, transparent 5%, rgba(var(--heart-color-rgb), 0.9) 5%, rgba(var(--heart-color-rgb), 0.9) 6%, transparent 6%),
        // 实圈（实心圆，半径6%，不透明）
        radial-gradient(circle at 50% 20%, var(--heart-color) 6%, transparent 6%),
        radial-gradient(circle at 50% 80%, var(--heart-color) 6%, transparent 6%),
        radial-gradient(circle at 20% 50%, var(--heart-color) 6%, transparent 6%),
        radial-gradient(circle at 80% 50%, var(--heart-color) 6%, transparent 6%),
        radial-gradient(circle at 35% 35%, var(--heart-color) 6%, transparent 6%),
        radial-gradient(circle at 65% 35%, var(--heart-color) 6%, transparent 6%),
        radial-gradient(circle at 35% 65%, var(--heart-color) 6%, transparent 6%),
        radial-gradient(circle at 65% 65%, var(--heart-color) 6%, transparent 6%),
        // 中心区域（保持不变）
        radial-gradient(circle at 50% 50%, var(--heart-color) 8%, transparent 8%),
        radial-gradient(circle at 50% 50%, transparent 6%, var(--heart-color) 6%, transparent 12%);

      background-size: 18% 18%; // 保持大尺寸确保可见
    }

    // 底部气泡组（可根据需要添加）
    &:after {
      display: none; // 暂时合并为单组动画更易调试
    }
    .svg-container::after {
     animation: ray-spread 0.6s ease-out forwards;
 }
  }
}

@keyframes keyframes-svg-filled {
  0% {
    transform: scale(0);
  }

  25% {
    transform: scale(1.2);
  }

  50% {
    transform: scale(1);
    filter: brightness(1.5);
  }
}


@keyframes ray-spread {
  0% {
    opacity: 0;
    transform: scale(0.5);  /* 保持从爱心外开始 */
  }
  50% {
    opacity: 0.8;
    transform: scale(1.5);  /* 关键修改：增大中间缩放（2.0→2.5） */
  }
  100% {
    opacity: 0;
    transform: scale(2.5);  /* 关键修改：增大最终缩放（2.5→3.0） */
  }
}

// 关键修改：统一爆炸动画
@keyframes explode {
  0% {
    background-position:
      50% 50%,
      50% 50%,
      // 中心实虚圈
      50% 20%,
      50% 80%,
      20% 50%,
      80% 50%,
      35% 35%,
      65% 35%,
      35% 65%,
      65% 65%,
      // 实圈初始位置
      50% 20%,
      50% 80%,
      20% 50%,
      80% 50%,
      35% 35%,
      65% 35%,
      35% 65%,
      65% 65%;
    background-size: 18% 18%; /* 与before中的background-size一致 */
    opacity: 1;
  }
  100% {
    background-position:
      50% 50%,
      50% 50%,
      // 缩小终点偏移量（原-20%→-10%，120%→110%）
      50% -10%,
      50% 110%,
      -10% 50%,
      110% 50%,
      20% 20%,
      80% 20%,
      20% 80%,
      80% 80%,
      // 虚圈终点同步调整
      50% -10%,
      50% 110%,
      -10% 50%,
      110% 50%,
      20% 20%,
      80% 20%,
      20% 80%,
      80% 80%;
    background-size: 18% 18%; /* 保持尺寸，避免消失 */
    opacity: 0; /* 仅通过透明度消失 */
  }
}
</style>
