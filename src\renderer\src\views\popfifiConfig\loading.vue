<template>
  <div class="home">

    <!-- 错误提示框 -->
    <div v-if="showError" class="error-toast">
      {{ errorMessage }}
    </div>


    <!-- 背景暗化层 -->
    <!-- <div class="background-overlay">
      
    </div> -->
    <img class="bk" src="@/assets/images/background.png">
    <!-- <img class="title" src="@/assets/images/popofifititle.png"> -->
    <div class="my-video-wraper">
      <BaseCommonLoopVideo :videoSrc="loadingVideo"  />
      <!-- <video src="@/assets/video/loading.mp4" autoplay loop muted></video> -->
    </div>
    <div class="progress-bar-wrapper">
      <div class="progress-bar-inner" :style="{ width: progress + '%' }"></div>
    </div>
    <!-- 中间轮播图 -->
    <!-- <div class="carousel-container">
      <div class="carousel-card">
        <div class="videobox">
          
        </div>
      </div>

      

    </div> -->

   <div class="loading-text">
      正在进行元宇宙穿越
    </div>

  </div>
</template>

<script>
import axios from 'axios'
// 注册 Swiper 模块
// SwiperCore.use([Autoplay])
import {uploadImg} from './uploadImg.js'
import testImg from '@/assets/test.jpg'
import { result } from 'lodash-es'
import BaseCommonLoopVideo from '@/components/base/CommonLoopVideo/index.vue'
import loadingVideo from '@/assets/video/loading_new_small.mp4'
import electronApi from '@/utils/electronApi'

import { useRouteParamsStore } from '@/store/routeParams'
export default {
  name: 'HomePage',
  components:{
    BaseCommonLoopVideo
  },
  data() {
    return {
      images: [
        new URL('@/assets/home/<USER>', import.meta.url),
        new URL('@/assets/home/<USER>', import.meta.url),
        new URL('@/assets/home/<USER>', import.meta.url),
      ],
      fgSwiper: null,
      progress: 0,
      showError: false,
      errorMessage: '',
      getRoundTimeout: null,
      getTimeout: null,
      loadingVideo
    }
  },
  mounted() {
    this.startProgress();
    // return
    
    this.generateAllTemplates();
  },
  unmounted() {
    this.getRoundTimeout && clearTimeout(this.getRoundTimeout)
    this.getTimeout && clearTimeout(this.getTimeout)
  },
  methods: {


    generatePrefix() {
      const now = new Date();
      const pad = (n) => n.toString().padStart(2, '0');

      const datePart = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}`;
      const timePart = `${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
      const randPart = Math.floor(Math.random() * 1000); // 0~999 随机数
      return `test_${datePart}_${timePart}_${randPart}`;
    },

    async generateAllTemplates() {
      // 从路由传值store中获取参数
      const useRouteParams = useRouteParamsStore()
      const routeData = useRouteParams.getRouteParams()

      let subjectFile = routeData?.subjectFile
      const templatecode = routeData?.code || '001'
      const gender = routeData?.gender || 'female';
      const refresh = routeData?.refresh ? "true" : "false";

      // const templateMap = {
      //   female: ['009', '106', '103', '102', '006'], //009
      //   male: ['001', '005', '113','104', '009']
      // };

      // // 构造不重复的模板序列：先处理用户选择的，再处理性别对应的
      // const templateSet = new Set(templateMap[gender]);
      // templateSet.delete(templatecode); // 避免重复
      // const orderedTemplateCodes = [templatecode, ...Array.from(templateSet)].slice(0, 5);

      const orderedTemplateCodes = routeData?.codes
      
      subjectFile = subjectFile || await this.getTestImageAsFile();

      // 检查是否有xingyunshike传递的特殊参数
      const avatarId = routeData?.avatarId
      const objectKey = routeData?.objectKey
      const taskId = routeData?.taskId

      let r = await uploadImg(subjectFile)
      console.log(r)

      // 如果有xingyunshike传递的参数，优先使用
      if (avatarId && objectKey) {
        r.avatarId = avatarId
        r.objectKey = objectKey
      }

      let client_id= localStorage.getItem('clientId')
      if (r.objectKey){
        let taskId = new Date().getTime().toString()
        const clientId = electronApi.globals.getClientId()
        let res = await axios.post('/api/v1.0/digital-avatar/task/2d/gen',{
          client_id: clientId?.screen_num,
          task_id: taskId,
          digital_avatar_id: r.avatarId,
          data:[
            {
              index: 0,
              styles: orderedTemplateCodes.map(e=>{return 'p_'+e}),
              avatar_image: r.objectKey
            }
          ]
        })
        console.log(res)
        console.log(orderedTemplateCodes)
        taskId = res.data.data.task_id
        this.getTimeout = setTimeout(() => {
            let f = e=>{
              const clientId = electronApi.globals.getClientId()
              axios.get('/api/v1.0/digital-avatar/task/2d/get',{
                params:{
                  "task_id": taskId,
                  "client_id": clientId?.screen_num
                }
              }).then(e=>{
                console.info('第一张：',new Date().getTime())
                console.log(e)
                let list = e.data.data.avatars[0].styles
                let index = list.findIndex(e=>e.status == 2)
                
                if (index!= -1){
                  let first = list[index]
                  clearTimeout(this.getRoundTimeout)

                  // 将结果数据存储到路由传值store中
                  const resultData = {
                    client_id: client_id,
                    results: first,
                    resultsId: index,
                    taskId: taskId,
                    avatarId: r.avatarId,
                    objectKey: r.objectKey,
                    remainCodes: orderedTemplateCodes,
                    code: templatecode,
                    subjectFile,
                    gender
                  }
                  useRouteParams.setRouteParams(resultData)

                  this.$router.push({
                    path: "/rebuild2-result"
                  })
                }else{
                  this.getRoundTimeout = setTimeout(f,1000)
                }
                
              }).catch(err=>{
                if (err?.code === 4000){
                  this.showErrorToast('获取结果失败：' + (err?.msg || '未知错误'))
                  this.getRoundTimeout = setTimeout(() => {
                    this.showError = false
                    this.$router.push({ path: '/welcome' })
                  }, 3000)
                }
                
              })
            }
            f()
        },5000)
      }
      
      return
      

      const resultList = [];

      for (let i = 0; i < orderedTemplateCodes.length; i++) {
        const code = orderedTemplateCodes[i];
        const result = await this.generateOneTemplate(subjectFile, code, refresh);

        if (i === 0 && !result) return
        if (result) resultList.push(result);

        if (i === 0 && result) {
          this.$router.push({
            path: "/rebuild2-result",
            state: {
              results: resultList,
              remainCodes: orderedTemplateCodes.slice(1),
              subjectFile,
              gender
            }
          });
        }
      }
    },
    async convertImageToFile(imageUrl, fileName = 'test.jpg') {
      try {
        // 获取图片的 Blob
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        
        // 创建 File 对象
        const file = new File([blob], fileName, { type: blob.type });
        return file;
      } catch (error) {
        console.error('转换图片到 File 对象失败:', error);
        this.showErrorToast('转换图片到 File 对象失败: ' + error.message);
        return null;
      }
    },
    
    async getTestImageAsFile() {
      // 将导入的静态资源路径转换为 URL
      const imageUrl = new URL(testImg, import.meta.url).href;
      return await this.convertImageToFile(imageUrl);
    },

    async generateOneTemplate(subjectFile, code, refresh) {


      const API_HOST = "http://***************:8333";
      //const API_HOST = "http://************:8333";
      const prefix = this.generatePrefix();
      const patternIndex = `p_${code}`;

      const uploadData = new FormData();
      uploadData.append("human_image_path", subjectFile);

      const queryParams = new URLSearchParams({
        prefix,
        pattern_index: patternIndex,
        refresh
      });

      try {
        const res = await fetch(`${API_HOST}/generate_image_Qstyle_face?${queryParams}`, {
          method: "POST",
          body: uploadData
        });

        if (!res.ok) {
          this.showErrorToast(`ComfyUI接口返回错误 ${res.status}，请检查服务是否存在`);
          return null;
        }

        const json = await res.json();
        const task = JSON.parse(json.data);
        const statusUrl = task.status_url.startsWith("http") ? task.status_url : API_HOST + task.status_url;

        return await this.pollStatus(statusUrl, code);
      } catch (e) {
        console.log("ComfyUI上传图片失败：" + (e?.message || "未知错误"))
        this.showErrorToast("ComfyUI上传图片失败：" + (e?.message || "未知错误"));
        return null;
      }
    },


    async pollStatus(statusUrl, code) {
      const API_HOST ="http://***************:8333";
      //const API_HOST = "http://************:8333";

      for (let i = 0; i < 20; i++) {
        await new Promise(r => setTimeout(r, 3000));
        try {
          const res = await fetch(statusUrl);
          const json = await res.json();
          if (json.status === "completed") {
            const localPath = json.local_gen_name;
            const relativePath = localPath.replace(/^.*output\//, 'output/');

            console.log('localPath:',localPath)
            const downloadUrl = `${API_HOST}/download/${encodeURIComponent(relativePath)}`;

            console.log('downloadUrl:',downloadUrl)
            const imageResponse = await fetch(downloadUrl);
            if (!imageResponse.ok) {
              console.warn(`下载失败：${imageResponse.status}`);
              this.showErrorToast(`ComfyUI图像下载失败：${imageResponse.status}`);
              return null;
            }

            const blob = await imageResponse.blob();
            const base64 = await this.convertBlobToBase64(blob);

            return {
              code,
              base64,
              outputPath: localPath
            };
          }
        } catch (e) {
          console.warn("轮询异常：", e);
          this.showErrorToast("轮询异常过程中出现异常: "+(e?.message || "未知错误"));
          return null;
        }
      }
      return null;
    },

    convertBlobToBase64(blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const result = reader.result;
          const base64Data = result.split(",")[1]; // 去掉 data:image/jpeg;base64, 前缀
          resolve(base64Data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    },


    showErrorToast(message) {
      this.errorMessage = message
      this.showError = true
      // 可选：5 秒后自动隐藏
      // setTimeout(() => {
      //   this.showError = false
      // }, 60000)
    },
    startProgress() {
      this.progress = 0
      const loop = () => {
        this.progress = 0
        const interval = setInterval(() => {
          if (this.progress < 100) {
            this.progress += 1
          } else {
            clearInterval(interval)
            setTimeout(loop, 300) // 稍作停顿后重启循环
          }
        }, 50) // 每 50ms 增加 1%，约 5 秒一轮
      }
      loop()
    },

    onSwiperReady(swiperInstance) {
      this.fgSwiper = swiperInstance
      console.log('前景 Swiper 准备完成')
    },
    onBGSwiperReady(swiperInstance){
      swiperInstance.slideNext(600)
    },
    onBgSlideChange() {
      setTimeout(() => {
        if (this.fgSwiper) {
          this.fgSwiper.slideNext(600)
        }
      }, 500)
    }
  }
}
</script>

<style lang="less" scoped>
.bk{
  height: 100vh;
  width: 100vw;
  position: absolute;
  object-fit: cover;
  z-index: -1;
}
.title{
  // height: 200rem;
  width: 90vw;
  position:absolute;
  z-index: 555;
  margin-top: 100rem;
}
// video{
//   border-radius: 50rem;
//   object-fit: contain;
//   width: 100%;
//   height: 100%;
// }
.videobox{
  // height: 100%;
  height: 1600rem;
  width: 100%;
  position: relative;
  overflow: hidden;
  video{
    border-radius: 50rem;
    object-fit: cover;
    width: 100%;
    height: 100%;
   
  }
}
.home {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  .background-swiper {
    position: absolute;
    inset: 0;
    z-index: 0;

    .swiper-slide {
      width: 100%;
      height: 100%;

      .bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: blur(3px) brightness(0.65);
      }
    }
  }

  .background-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0.6, 0.6, 0.6, 0.2);
    z-index: 1;
  }
  .my-video-wraper {
    width: 100%;
    height: 100%;
    margin: auto;
    position: relative;
    video {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }

  .error-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff4d4f;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    z-index: 9999;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 0.3s ease-out;
  }

  @keyframes fadeInDown {
    0% {
      opacity: 0;
      transform: translate(-50%, -20px);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }





  .carousel-container {
    flex: 5;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: -10vw;
    flex-direction: column;

    .carousel-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(2px);
      border: 1px solid rgba(255, 255, 255, 0.4);
      padding: 12px;
      border-radius: 20px;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
      width: 65vw;
      position: relative; // ✅ 保证内部绝对定位元素相对本容器定位

      .carousel-title {
        position: relative; // ✅ 让其在模糊层之上显示
        z-index: 2;
        text-align: center;
        font-size: 5vw;
        font-weight: bold;
        color:rgb(230,230,230); // ✅ 改为纯黑，更清晰
        margin-bottom: 10px;
        //background-color: rgba(255, 255, 255, 0.85); // ✅ 给文字加浅底背景
        padding: 8px 12px;
        border-radius: 12px;
        display: inline-block;
        margin-left: auto;
        margin-right: auto;
      }

      .carousel-image {
        width: 100%;
        border-radius: 10px;
        object-fit: cover;
        display: block;
      }
    }

  }

  .progress-bar-wrapper {
    width: 65vw;
    height: 12px;
    position: absolute;
    bottom: 150rem;
    // margin-top: 150rem;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
    z-index: 2;

    .progress-bar-inner {
      height: 100%;
      background: linear-gradient(to right, #ff4d4f, #ff7875);
      transition: width 0.2s linear;
    }
  }
    .loading-text {
    position: absolute;
    bottom: 220rem;
    width: 100%;
    font-size: 90rem;
    text-align: center;
    letter-spacing: 14.5rem;
    color: #fff;
    font-weight:700;
  }


}
</style>
