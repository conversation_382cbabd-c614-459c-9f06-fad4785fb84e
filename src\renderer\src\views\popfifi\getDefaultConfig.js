import axios from 'axios'
import {useSinglePhotoStore} from '@/store/single'
import {useConfigStore} from '@/store/config'
// let isInit = false
const getStyles = async ()=>{
    let store = useSinglePhotoStore()
    await axios.get('/api/v1.0/device/get_style').then(e=>{
        store.setStyles(e.data.data)
    })
}
const getGoodLst = ()=>{
    axios.post('/api/v1.0/device/get_goods_list').then(e=>{
        let data = e.data.data.goods_list.find(e=>e.type == 'NFCKeyChain')
        this.amount = data.price
        this.vipAmount = data.price
        this.good = data
    })
}
const initConfig = async ()=>{
    // if(isInit) return
    // 九宫格配置
    await getStyles()
    await useConfigStore().getGoodList();
    await useConfigStore().getDiscountList();
    console.log('ss')
    
    // getGoodLst()
    // isInit = true
}

export default initConfig